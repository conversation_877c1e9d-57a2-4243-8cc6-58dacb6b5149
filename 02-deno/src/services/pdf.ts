import pdf2pic from 'pdf2pic';
import { join } from '@std/path';
import type { PDFPageInfo } from '../types.ts';
import { logger } from '../utils/logger.ts';
import { FileUtils } from '../utils/file.ts';

export class PDFService {
  private pdfPath: string;
  private tempDir: string;
  private totalPages: number | null = null;

  constructor(pdfPath: string, tempDir: string) {
    this.pdfPath = pdfPath;
    this.tempDir = tempDir;
  }

  async init(): Promise<void> {
    await FileUtils.ensureDirectoryExists(this.tempDir);
    await this.getTotalPages();
  }

  async getTotalPages(): Promise<number> {
    if (this.totalPages !== null) {
      return this.totalPages;
    }

    try {
      // Use pdf2pic to get page count
      const convert = pdf2pic.fromPath(this.pdfPath, {
        density: 100,
        saveFilename: 'temp_page_count',
        savePath: this.tempDir,
        format: 'png',
        width: 100,
        height: 100,
      });

      // Convert just the first page to get total page count
      const result = await convert(1, { responseType: 'image' });

      if (Array.isArray(result)) {
        // Get page count from the conversion result
        // This is a workaround - we'll need to implement a better method
        this.totalPages = await this.getPageCountFromPDF();
      } else {
        this.totalPages = 1;
      }

      await logger.info(`PDF has ${this.totalPages} pages`);
      return this.totalPages;
    } catch (error) {
      await logger.error('Failed to get total pages from PDF', error);
      throw error;
    }
  }

  private async getPageCountFromPDF(): Promise<number> {
    // This is a simplified implementation
    // In a real scenario, you might want to use a more robust PDF library
    try {
      const command = new Deno.Command('pdfinfo', {
        args: [this.pdfPath],
        stdout: 'piped',
        stderr: 'piped',
      });

      const { code, stdout } = await command.output();

      if (code === 0) {
        const output = new TextDecoder().decode(stdout);
        const match = output.match(/Pages:\s+(\d+)/);
        if (match) {
          return parseInt((match[1]!), 10);
        }
      }
    } catch {
      // Fallback: assume reasonable number of pages
      await logger.warn('Could not determine page count, assuming 100 pages');
      return 100;
    }

    return 100; // Default fallback
  }

  async convertPageToImage(pageNumber: number): Promise<PDFPageInfo> {
    try {
      await logger.progress(`Converting page ${pageNumber} to image`);

      const convert = pdf2pic.fromPath(this.pdfPath, {
        density: 300, // High quality for OCR
        saveFilename: `page_${pageNumber}`,
        savePath: this.tempDir,
        format: 'png',
        quality: 100,
      });

      const result = await convert(pageNumber, { responseType: 'image' });

      if (!result || (Array.isArray(result) && result.length === 0)) {
        throw new Error(`Failed to convert page ${pageNumber}`);
      }

      const imagePath = Array.isArray(result) ? result[0].path : result.path;

      // Get image dimensions and file size
      const stat = await Deno.stat(imagePath);

      // Use sharp to get image dimensions
      const sharp = (await import('sharp')).default;
      const metadata = await sharp(imagePath).metadata();

      const pageInfo: PDFPageInfo = {
        pageNumber,
        imagePath,
        width: metadata.width || 0,
        height: metadata.height || 0,
        fileSize: stat.size,
      };

      await logger.debug(`Page ${pageNumber} converted`, pageInfo);
      return pageInfo;
    } catch (error) {
      await logger.error(`Failed to convert page ${pageNumber} to image`, error);
      throw error;
    }
  }

  async convertPagesToImages(pageNumbers: number[]): Promise<PDFPageInfo[]> {
    const results: PDFPageInfo[] = [];

    for (const pageNumber of pageNumbers) {
      try {
        const pageInfo = await this.convertPageToImage(pageNumber);
        results.push(pageInfo);
      } catch (error) {
        await logger.error(`Failed to convert page ${pageNumber}`, error);
        // Continue with other pages
      }
    }

    return results;
  }

  getRandomPageNumbers(count: number): number[] {
    if (!this.totalPages) {
      throw new Error('Total pages not determined. Call init() first.');
    }

    const pageNumbers: number[] = [];
    const maxPages = Math.min(count, this.totalPages);

    while (pageNumbers.length < maxPages) {
      const randomPage = Math.floor(Math.random() * this.totalPages) + 1;
      if (!pageNumbers.includes(randomPage)) {
        pageNumbers.push(randomPage);
      }
    }

    return pageNumbers.sort((a, b) => a - b);
  }

  getAllPageNumbers(): number[] {
    if (!this.totalPages) {
      throw new Error('Total pages not determined. Call init() first.');
    }

    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  async cleanup(): Promise<void> {
    try {
      await FileUtils.cleanupTempFiles(this.tempDir);
      await logger.info('PDF service cleanup completed');
    } catch (error) {
      await logger.warn('Failed to cleanup PDF service', error);
    }
  }
}
